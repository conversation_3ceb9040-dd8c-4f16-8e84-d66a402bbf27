{"name": "app-sync-worker", "version": "2.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "deploy:dev": "wrangler deploy -e=dev", "dev": "wrangler dev -e=dev", "start": "wrangler dev", "test": "vitest run"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.7.5", "vitest": "~3.0.7", "wrangler": "^4.7.2"}, "dependencies": {"@hono/zod-validator": "^0.4.3", "hono": "^4.7.7", "ua-parser-js": "^2.0.3", "zod": "^3.24.3", "pdf-lib": "^1.17.1"}}