export async function getNextAgentRoundRobin(env) {
  const indexKey = 'round-robin:index';

  const agentsList = JSON.parse(await env.KV.get('round-robin:list')) || env.SAMPLE_AGENTS;
  // Get current index
  let index = parseInt((await env.KV.get(indexKey)) || '0');

  const agent = {
    ...agentsList[index],
    assigned_at: new Date().toISOString(),
  };

  // Calculate next index and save it
  const nextIndex = (index + 1) % agentsList.length;
  await env.KV.put(indexKey, nextIndex.toString());

  return agent;
}
