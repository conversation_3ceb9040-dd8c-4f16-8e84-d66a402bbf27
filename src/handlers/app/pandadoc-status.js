import { createFactory } from 'hono/factory';
import { ensureApplicationByUUID } from './get-app';
import { AppError } from '../../helpers';
import { getPandaDoc } from '../../pandadoc';

const factory = createFactory();

export const isPandaDocSigned = async (env, documentId) => {
  if (!documentId) return false;

  try {
    const document = await getPandaDoc(env, documentId);
    return document?.status === 'document.completed';
  } catch (e) {
    console.error(e);
    return false;
  }
};

export const pandadocStatusHandlers = factory.createHandlers(ensureApplicationByUUID, async (c) => {
  const application = c.get('application');

  if (!application.pandadoc?.document?.id) {
    throw new AppError('PandaDoc document not found for this application', 404, 'pandadocStatus');
  }

  const signed = await isPandaDocSigned(c.env, application.pandadoc.document.id);

  return c.json({ signed });
});
