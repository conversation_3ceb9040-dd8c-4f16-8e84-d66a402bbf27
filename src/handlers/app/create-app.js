import { createFactory } from 'hono/factory';
import { zValidator } from '@hono/zod-validator';
import { preQualifySchema } from '../../schema/prequal';
import { AppError, safeUTMObject, updateApplicationInKV } from '../../helpers';
import { z } from 'zod';
import { getMeta, isPrequalApproved } from '../../utils';
import { getNextAgentRoundRobin } from '../../round-robin';

const factory = createFactory();

const validator = zValidator(
  'json',
  z.object({ preQualifyFields: preQualifySchema, utm: z.object({}).passthrough().optional() }),
  async (result) => {
    if (!result.success) {
      throw new AppError('Validation Error: Invalid preQualifyFields', 400, 'validationError', result.error);
    }
  }
);

export const createtAppHandlers = factory.createHandlers(validator, async (c) => {
  const { preQualifyFields, utm } = c.req.valid('json');

  // Trim all string values in preQualifyFields
  Object.keys(preQualifyFields).forEach((key) => {
    if (typeof preQualifyFields[key] === 'string') {
      preQualifyFields[key] = preQualifyFields[key].trim();
    }
  });

  const safeUtm = safeUTMObject(utm);

  const timestamp = c.get('timestamp');
  const uuid = crypto.randomUUID();

  const { prequalApproved, reason, approvalAmount } = isPrequalApproved(preQualifyFields);
  const status = prequalApproved ? 'PREQUAL_APPROVED' : 'PREQUAL_DENIED';

  let agent;
  const agentsList = (await c.env.KV.get('agents:list', { type: 'json' })) || c.env.SAMPLE_AGENTS;
  const utmSource = utm?.utm_source?.trim().toLowerCase();
  const utmRep = utm?.utm_rep?.trim().toLowerCase();

  console.log({ utmSource, utmRep });

  const source = utmRep || utmSource;

  const preAssignedAgent =
    source === 'test'
      ? c.env.TEST_AGENT
      : agentsList.find((agent) => {
          const emailPrefix = agent.email.split('@')[0];
          return emailPrefix.toLowerCase() === source;
        });

  if (preAssignedAgent) {
    console.log({ preAssignedAgent });
    agent = preAssignedAgent;
  } else if (prequalApproved) {
    console.log('No agent, getting next from round robin');
    agent = await getNextAgentRoundRobin(c.env);
  }

  const application = {
    uuid,
    version: c.env.VERSION,
    status,
    created_at: timestamp,
    preQualifyFields: preQualifyFields,
    agent,
    utm: safeUtm,
  };

  application.meta = { initiated: getMeta(c.req.raw, timestamp) };

  if (prequalApproved) {
    application.approvalAmount = approvalAmount;
  }

  if (!prequalApproved) {
    application.reason = reason;
  }

  await Promise.all([
    updateApplicationInKV(c.env, uuid, application, timestamp),
    c.env.QUEUE.send({ type: 'admin', application }),
    c.env.QUEUE.send({ type: 'email', application }, { delaySeconds: c.env.EMAIL_QUEUE_DELAY }),
    c.env.QUEUE.send({ type: 'salesforce', application }),
  ]);

  return c.json({ uuid, status, agent, reason, approvalAmount }, 201);
});
