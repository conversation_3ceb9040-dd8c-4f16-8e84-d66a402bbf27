import { createFactory } from 'hono/factory';
import { AppError } from '../helpers';

const factory = createFactory();

export const validateRepHandlers = factory.createHandlers(async (c) => {
  const rep = c.req.param('rep')?.trim().toLowerCase();

  if (!rep) {
    throw new AppError('Missing rep parameter', 400, 'validationError');
  }

  const agentsList = (await c.env.KV.get('agents:list', { type: 'json' })) || c.env.SAMPLE_AGENTS;

  console.log(agentsList);

  const isValidRep = agentsList.some((agent) => {
    const emailPrefix = agent.email.split('@')[0];
    return emailPrefix.toLowerCase() === rep;
  });

  return c.json({
    rep,
    valid: isValidRep,
  });
});
