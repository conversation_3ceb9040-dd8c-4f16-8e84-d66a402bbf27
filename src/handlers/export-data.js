import { createFactory } from 'hono/factory';

const factory = createFactory();

export const validateExportToken = async (c, next) => {
  const token = c.req.query('token');

  if (token !== c.env.DATA_EXPORT_TOKEN) {
    return c.text('Unauthorized', 401);
  }

  await next();
};

async function getAllAppKeys(KV) {
  const prefix = 'app:';
  let cursor;
  const keys = [];

  while (true) {
    const res = await KV.list({ prefix, cursor });
    for (const k of res.keys) {
      if (!k.name.includes(':', prefix.length)) {
        keys.push(k.name);
      }
    }
    if (res.list_complete) break;
    cursor = res.cursor;
  }

  return keys;
}

function convertToNYFormat(isoString) {
  if (!isoString) return;
  const date = new Date(isoString);

  const options = {
    timeZone: 'America/New_York',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  };

  return new Intl.DateTimeFormat('en-US', options).format(date);
}

function prepareExportData(application) {
  const {
    uuid,
    status,
    preQualifyFields: prequal,
    applicationFields: app,
    agent,
    utm,
    created_at,
    submitted_at,
    signed_at,
    completed_at,
  } = application;

  const {
    fundingAmount,
    purpose,
    topPriority,
    timeline,
    businessName,
    monthlyRevenue,
    businessStartDate,
    firstName,
    lastName,
    email,
    phone,
    estimatedFICO,
  } = prequal;

  const { utm_campaign, utm_site, utm_medium, utm_content, utm_term, utm_source, utm_rep } = utm || {};

  const exportData = {
    uuid,
    status,
    complete: status === 'APP_COMPLETED',
    created_at: convertToNYFormat(created_at) || '',
    fundingAmount,
    purpose,
    topPriority,
    timeline,
    businessName,
    monthlyRevenue,
    monthlyRevenue_midpoint: monthlyRevenue
      ?.replace('+', '')
      .split('-')
      .reduce((a, b) => (+a + +b) / 2),
    businessStartDate,
    firstName,
    lastName,
    email,
    phone,
    estimatedFICO: estimatedFICO || '',
    estimatedFICO_midpoint: Math.round(estimatedFICO?.split('-').reduce((a, b) => (+a + +b) / 2)),
    agent_name: agent?.name || '',
    utm_source: utm_source || '',
    utm_campaign: utm_campaign || '',
    utm_site: utm_site || '',
    utm_rep: utm_rep || '',
    utm_medium: utm_medium || '',
    utm_content: utm_content || '',
    utm_term: utm_term || '',
    direct_rep: Boolean(utm?.utm_rep) && Object.entries(utm).every(([key, value]) => key === 'utm_rep' || !value),
    submitted_at: convertToNYFormat(submitted_at) || '',
    signed_at: convertToNYFormat(signed_at) || '',
    completed_at: convertToNYFormat(completed_at) || '',
    created_at_utc: created_at || '',
  };

  return exportData;
}

function jsonToCSV(data, delimiter = ',') {
  if (!Array.isArray(data) || data.length === 0) return '';

  const headers = Object.keys(data[0]);
  const rows = data.map((obj) => headers.map((h) => obj[h] ?? '').join(delimiter));

  return [headers.join(delimiter), ...rows].join('\n');
}

export const exportDataHandlers = factory.createHandlers(validateExportToken, async (c) => {
  const { type } = c.req.query();
  const keys = await getAllAppKeys(c.env.KV);
  const data = [];
  for (const key of keys) {
    const application = await c.env.KV.get(key, 'json');
    data.push(prepareExportData(application));
  }

  data.sort((a, b) => new Date(a.created_at_utc) - new Date(b.created_at_utc));

  if (type === 'json') return c.json(data);

  const csvData = jsonToCSV(data, '\t');
  const filename = `${c.env.DEV_MODE ? 'DEV-' : ''}app-export-${new Date().toISOString()}.csv`;
  c.header('Content-Type', 'text/csv');
  c.header('Content-Disposition', `attachment; filename="${filename}"`);
  c.header('Content-Length', csvData.length);
  return c.body(csvData);
});
