import { bodyLimit } from 'hono/body-limit';
import { createMiddleware } from 'hono/factory';
import { AppError } from '../helpers';

const MAX_BODY_SIZE_BYTES = 1 * 1024 * 1024; // 1 MB
const FILE_BODY_SIZE_BYTES = 10 * 1024 * 1024; // 10 MB

export const apiBodyLimitMiddleware = createMiddleware(async (c, next) => {
  const path = c.req.path;
  const isCompleteAppEndpoint = path.match(/^\/app\/[^/]+\/complete$/);

  await bodyLimit({
    maxSize: isCompleteAppEndpoint ? FILE_BODY_SIZE_BYTES : MAX_BODY_SIZE_BYTES,
    onError: (c) => {
      throw new AppError('Payload too large', 413, 'bodyLimitMiddleware');
    },
  })(c, next);
});

// export const fileBodyLimitMiddleware = bodyLimit(10 * 1024 * 1024); // 10 MB
