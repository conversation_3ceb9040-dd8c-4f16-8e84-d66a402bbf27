const log = (...args) => console.log("SALESFORCE:\t", ...args);

/**
 * Handle Salesforce queue messages
 * @param {Object} message - The message object containing application data
 * @param {Object} env - Environment variables and bindings
 */
export async function salesforceQueueHandler(message, env) {
  log('salesforceQueueHandler called');

  try {
    const { application } = message;

    if (!application) {
      console.error('No application data in message');
      return;
    }

    log(`Processing Salesforce data for application ${application.uuid}`);

    log('Salesforce processing completed successfully');
  } catch (error) {
    console.error(`Error processing Salesforce data: ${error.message}`);
    throw error;
  }
}
