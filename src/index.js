import { Hono } from 'hono';

import { corsMiddleware } from './middlewares/cors';
import { rateLimitMiddleware } from './middlewares/rate-limit';
import { apiBodyLimitMiddleware } from './middlewares/body-limit';
import { attachTimestampMiddleware } from './middlewares/attach-timestamp';
import { emailQueueHandler } from './queues/email';
import { salesforceQueueHandler } from './queues/salesforce';
import { adminQueueHandler } from './queues/admin';
import { createtAppHandlers } from './handlers/app/create-app';
import { getAppHandlers } from './handlers/app/get-app';
import { submitAppHandlers } from './handlers/app/submit-app';
import { editAppHandlers } from './handlers/app/edit-app';
import { signAppHandlers } from './handlers/app/sign-app';
import { completeAppHandlers } from './handlers/app/complete-app';
import { pandadocStatusHandlers } from './handlers/app/pandadoc-status';
import { validateRepHandlers } from './handlers/validate-rep';
import { errorHandler } from './handlers/error/error-handler';
import { exportDataHandlers } from './handlers/export-data';
import { logErrorToKV } from './helpers';

const app = new Hono();

app.use('*', corsMiddleware);
app.use('*', rateLimitMiddleware);
app.use('*', apiBodyLimitMiddleware);
app.use('*', attachTimestampMiddleware);

app.get('/', (c) => c.text('OK'));

app.post('/app', ...createtAppHandlers);
app.get('/app/:uuid', ...getAppHandlers);

app.post('/app/:uuid/submit', ...submitAppHandlers);
app.post('/app/:uuid/edit', ...editAppHandlers);
app.post('/app/:uuid/sign', ...signAppHandlers);
app.post('/app/:uuid/complete', ...completeAppHandlers);

app.get('/app/:uuid/pandadoc/status', ...pandadocStatusHandlers);

app.get('/reps/:rep/validate', ...validateRepHandlers);

app.get('/export', ...exportDataHandlers);

app.notFound((c) => c.text('Not Found', 404));

app.onError(errorHandler);

export default {
  fetch: app.fetch,

  async queue(batch, env) {
    // disable queue while running tests
    const IS_TESTING = import.meta.env ? import.meta.env?.MODE === 'test' : false;
    if (IS_TESTING) {
      return;
    }
    console.log(`[QUEUE] Processing ${batch.messages.length} messages from queue: ${batch.queue}`);

    for (const message of batch.messages) {
      console.log(`[QUEUE] Processing message:`, JSON.stringify(message));
      try {
        // Parse the message body if it's a string
        const data = typeof message.body === 'string' ? JSON.parse(message.body) : message.body;

        console.log(`[QUEUE] type: ${data.type}`);

        if (data.type === 'email') {
          await emailQueueHandler(data, env);
        } else if (data.type === 'salesforce') {
          await salesforceQueueHandler(data, env);
        } else if (data.type === 'admin') {
          await adminQueueHandler(data, env);
        } else {
          console.warn(`[QUEUE] Unknown message type: ${data.type}`);
        }

        // Acknowledge the message was processed successfully
        message.ack();
      } catch (error) {
        const { errorId } = await logErrorToKV(env, error, {
          source: error?.source || 'queueHandler',
          statusCode: error?.statusCode || 500,
        });

        console.error(`[QUEUE] Error processing message:${errorId} - ${error?.message}`);
        // Retry the message
        message.retry();
      }
    }
  },
};
